<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI API 真实调用测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8f9fa;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .config-section {
            background: #e8f0fe;
            border: 1px solid #1a73e8;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #1a73e8;
            padding-bottom: 10px;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        .result {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            font-size: 12px;
            border: 1px solid #ddd;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #1557b0;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .loading {
            opacity: 0.6;
        }
        .network-info {
            background: #f0f8ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 12px;
            border: 1px solid #b3d9ff;
        }
        .test-content {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            font-style: italic;
            border: 1px solid #ced4da;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 AI API 真实调用测试</h1>
        <p>验证AI功能是否使用真实API调用，确保没有模拟数据</p>
    </div>

    <div class="test-section config-section">
        <h3>⚙️ AI配置设置</h3>
        <div class="form-group">
            <label for="apiProvider">API提供商:</label>
            <select id="apiProvider">
                <option value="siliconflow">SiliconFlow (硅基流动)</option>
                <option value="openrouter">OpenRouter</option>
                <option value="deepseek">DeepSeek</option>
                <option value="moonshot">Moonshot AI</option>
            </select>
        </div>
        <div class="form-group">
            <label for="apiKey">API密钥:</label>
            <input type="password" id="apiKey" placeholder="请输入真实的API密钥">
        </div>
        <div class="form-group">
            <label for="model">模型:</label>
            <select id="model">
                <option value="THUDM/GLM-Z1-9B-0414">GLM-Z1-9B-0414</option>
                <option value="Qwen/Qwen2.5-72B-Instruct">Qwen2.5-72B-Instruct</option>
                <option value="deepseek-ai/DeepSeek-V2.5">DeepSeek-V2.5</option>
            </select>
        </div>
        <button onclick="saveConfig()">💾 保存配置</button>
        <button onclick="testConnection()">🔗 测试连接</button>
        <div id="config-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>🏷️ 标签生成测试</h3>
        <div class="test-content" id="tag-test-content">
            OpenAI 宣布其最新的旗舰模型 GPT-4o 现已通过 API 向所有开发者开放。该模型在处理多模态输入方面表现出色，并且响应速度更快、成本更低。这一发布标志着人工智能技术的又一重大突破，为开发者提供了更强大的工具来构建创新应用。
        </div>
        <button onclick="testTagGeneration()">🚀 测试标签生成</button>
        <div id="tag-result" class="result"></div>
        <div class="network-info">
            <strong>网络请求监控:</strong> 请打开浏览器开发者工具的Network标签，观察是否有真实的API请求发送到配置的API端点。
        </div>
    </div>

    <div class="test-section">
        <h3>🌐 翻译功能测试</h3>
        <div class="test-content" id="translation-test-content">
            Artificial Intelligence has revolutionized the way we interact with technology. From voice assistants to autonomous vehicles, AI is transforming industries and creating new possibilities for innovation.
        </div>
        <button onclick="testTranslation()">🚀 测试翻译功能</button>
        <div id="translation-result" class="result"></div>
        <div class="network-info">
            <strong>网络请求监控:</strong> 观察Network标签中的请求，确认翻译功能发送真实的API请求。
        </div>
    </div>

    <div class="test-section">
        <h3>📊 测试结果总结</h3>
        <div id="summary-result" class="result">
            等待测试完成...
        </div>
    </div>

    <script type="module">
        let testResults = {
            configSaved: false,
            connectionTested: false,
            tagGeneration: false,
            translation: false
        };

        // 保存配置
        window.saveConfig = async function() {
            const resultDiv = document.getElementById('config-result');
            try {
                resultDiv.className = 'result';
                resultDiv.textContent = '正在保存配置...';

                const provider = document.getElementById('apiProvider').value;
                const apiKey = document.getElementById('apiKey').value;
                const model = document.getElementById('model').value;

                if (!apiKey.trim()) {
                    throw new Error('请输入API密钥');
                }

                // 获取提供商配置
                const providerConfigs = {
                    siliconflow: { baseUrl: 'https://api.siliconflow.cn/v1' },
                    openrouter: { baseUrl: 'https://openrouter.ai/api/v1' },
                    deepseek: { baseUrl: 'https://api.deepseek.com/v1' },
                    moonshot: { baseUrl: 'https://api.moonshot.cn/v1' }
                };

                const config = {
                    provider: provider,
                    apiKey: apiKey,
                    model: model,
                    baseUrl: providerConfigs[provider].baseUrl,
                    temperature: 0.7,
                    maxTokens: 1000,
                    timeout: 60000
                };

                // 保存到Chrome存储
                if (typeof chrome !== 'undefined' && chrome.storage) {
                    await chrome.storage.sync.set({ aiConfig: config });
                } else {
                    // 非扩展环境，保存到localStorage
                    localStorage.setItem('aiConfig', JSON.stringify(config));
                }

                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 配置已保存！\n\n提供商: ${provider}\n模型: ${model}\nAPI地址: ${config.baseUrl}`;
                testResults.configSaved = true;
                updateSummary();
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 配置保存失败: ${error.message}`;
            }
        };

        // 测试连接
        window.testConnection = async function() {
            const resultDiv = document.getElementById('config-result');
            try {
                if (!testResults.configSaved) {
                    throw new Error('请先保存配置');
                }

                resultDiv.className = 'result';
                resultDiv.textContent = '正在测试API连接...';

                const config = await getConfig();
                
                const response = await fetch(`${config.baseUrl}/models`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${config.apiKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ API连接成功！\n\n状态码: ${response.status}\n可用模型数量: ${data.data ? data.data.length : '未知'}`;
                    testResults.connectionTested = true;
                } else {
                    throw new Error(`API连接失败: ${response.status} ${response.statusText}`);
                }
                updateSummary();
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 连接测试失败: ${error.message}`;
            }
        };

        // 测试标签生成
        window.testTagGeneration = async function() {
            const resultDiv = document.getElementById('tag-result');
            try {
                if (!testResults.connectionTested) {
                    throw new Error('请先测试API连接');
                }

                resultDiv.className = 'result';
                resultDiv.textContent = '正在调用真实API生成标签...';

                const config = await getConfig();
                const content = document.getElementById('tag-test-content').textContent;

                const prompt = `请为以下内容生成3-5个相关标签，用中文回答，每个标签用#开头，标签之间用空格分隔。

要求：
1. 标签要准确反映内容主题
2. 标签要简洁明了，2-4个字为佳
3. 每个标签必须以#开头
4. 标签之间用空格分隔
5. 不要添加其他解释文字

示例格式：#技术 #AI #编程 #开发

输入文本: """
${content}
"""
输出:`;

                const startTime = Date.now();
                const response = await fetch(`${config.baseUrl}/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${config.apiKey}`
                    },
                    body: JSON.stringify({
                        model: config.model,
                        messages: [{ role: 'user', content: prompt }],
                        temperature: config.temperature,
                        max_tokens: config.maxTokens,
                        stream: false
                    })
                });

                const endTime = Date.now();
                const responseTime = endTime - startTime;

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorData.error?.message || '未知错误'}`);
                }

                const data = await response.json();

                if (data.choices && data.choices.length > 0) {
                    const result = data.choices[0].message.content;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 标签生成成功！（真实API调用）\n\n响应时间: ${responseTime}ms\n生成的标签: ${result.trim()}\n\nAPI响应详情:\n${JSON.stringify(data, null, 2)}`;
                    testResults.tagGeneration = true;
                } else {
                    throw new Error('API响应格式错误');
                }
                updateSummary();
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 标签生成失败: ${error.message}`;
            }
        };

        // 测试翻译功能
        window.testTranslation = async function() {
            const resultDiv = document.getElementById('translation-result');
            try {
                if (!testResults.connectionTested) {
                    throw new Error('请先测试API连接');
                }

                resultDiv.className = 'result';
                resultDiv.textContent = '正在调用真实API进行翻译...';

                const config = await getConfig();
                const content = document.getElementById('translation-test-content').textContent;

                const prompt = `请将以下英文内容翻译成中文，保持原意，只返回翻译结果：\n\n${content}`;

                const startTime = Date.now();
                const response = await fetch(`${config.baseUrl}/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${config.apiKey}`
                    },
                    body: JSON.stringify({
                        model: config.model,
                        messages: [{ role: 'user', content: prompt }],
                        temperature: config.temperature,
                        max_tokens: config.maxTokens,
                        stream: false
                    })
                });

                const endTime = Date.now();
                const responseTime = endTime - startTime;

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorData.error?.message || '未知错误'}`);
                }

                const data = await response.json();

                if (data.choices && data.choices.length > 0) {
                    const result = data.choices[0].message.content;
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 翻译成功！（真实API调用）\n\n响应时间: ${responseTime}ms\n翻译结果: ${result.trim()}\n\nAPI响应详情:\n${JSON.stringify(data, null, 2)}`;
                    testResults.translation = true;
                } else {
                    throw new Error('API响应格式错误');
                }
                updateSummary();
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 翻译失败: ${error.message}`;
            }
        };

        // 获取配置
        async function getConfig() {
            if (typeof chrome !== 'undefined' && chrome.storage) {
                const result = await chrome.storage.sync.get('aiConfig');
                return result.aiConfig;
            } else {
                const config = localStorage.getItem('aiConfig');
                return config ? JSON.parse(config) : null;
            }
        }

        // 更新总结
        function updateSummary() {
            const summaryDiv = document.getElementById('summary-result');
            const total = Object.keys(testResults).length;
            const passed = Object.values(testResults).filter(Boolean).length;

            let summary = `测试进度: ${passed}/${total}\n\n`;
            summary += `✅ 配置保存: ${testResults.configSaved ? '通过' : '待测试'}\n`;
            summary += `✅ 连接测试: ${testResults.connectionTested ? '通过' : '待测试'}\n`;
            summary += `✅ 标签生成: ${testResults.tagGeneration ? '通过' : '待测试'}\n`;
            summary += `✅ 翻译功能: ${testResults.translation ? '通过' : '待测试'}\n\n`;

            if (passed === total) {
                summary += `🎉 所有测试通过！AI功能正在使用真实API调用，没有模拟数据。`;
                summaryDiv.className = 'result success';
            } else {
                summary += `⏳ 请继续完成剩余测试...`;
                summaryDiv.className = 'result warning';
            }

            summaryDiv.textContent = summary;
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 AI API 真实调用测试页面已加载');
            updateSummary();
        });
    </script>
</body>
</html>
