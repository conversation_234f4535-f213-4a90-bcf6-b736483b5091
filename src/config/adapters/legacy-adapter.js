// 旧版本配置适配器
// 为旧版本代码提供统一的配置接口，确保向后兼容性

import { configManager } from '../config-manager.js';
import { PromptTemplateEngine, TEMPLATE_PRESETS } from '../prompts.js';

/**
 * 旧版本配置适配器
 * 提供与旧版本代码兼容的配置接口
 */
export class LegacyConfigAdapter {
  constructor() {
    this.initialized = false;
  }

  /**
   * 初始化适配器
   */
  async initialize() {
    if (this.initialized) return;
    
    await configManager.initialize();
    this.initialized = true;
  }

  /**
   * 获取AI配置（兼容旧版本格式）
   * @returns {Promise<object>} AI配置对象
   */
  async getAIConfig() {
    await this.initialize();
    
    const aiConfig = await configManager.getConfig('ai');
    
    // 转换为旧版本格式
    return {
      apiUrl: `${aiConfig.baseUrl}/chat/completions`,
      apiKey: aiConfig.apiKey,
      model: aiConfig.model,
      temperature: aiConfig.temperature,
      max_tokens: aiConfig.maxTokens,
      timeout: aiConfig.timeout
    };
  }

  /**
   * 获取Flomo配置（兼容旧版本格式）
   * @returns {Promise<object>} Flomo配置对象
   */
  async getFlomoConfig() {
    await this.initialize();
    
    const flomoConfig = await configManager.getConfig('flomo');
    
    return {
      apiUrl: flomoConfig.apiUrl,
      timeout: flomoConfig.timeout
    };
  }

  /**
   * 获取侧边栏配置（兼容旧版本格式）
   * @returns {Promise<object>} 侧边栏配置对象
   */
  async getSidepanelConfig() {
    await this.initialize();
    
    const sidepanelConfig = await configManager.getConfig('sidepanel');
    
    return {
      autoClose: sidepanelConfig.autoClose,
      autoCloseDelay: sidepanelConfig.autoCloseDelay,
      theme: sidepanelConfig.theme,
      fontSize: sidepanelConfig.fontSize
    };
  }

  /**
   * 获取标签生成Prompt（兼容旧版本）
   * @param {string} content - 内容文本
   * @param {string} type - 模板类型 ('structured', 'simple', 'formatted')
   * @returns {string} 生成的prompt
   */
  getTagGenerationPrompt(content, type = 'structured') {
    return PromptTemplateEngine.renderTemplate('tagGeneration', type, { content });
  }

  /**
   * 获取翻译Prompt（兼容旧版本）
   * @param {string} content - 内容文本
   * @param {string} targetLang - 目标语言
   * @returns {string} 生成的prompt
   */
  getTranslationPrompt(content, targetLang = null) {
    const type = targetLang ? 'targeted' : 'smart';
    return PromptTemplateEngine.renderTemplate('translation', type, { 
      content, 
      targetLang 
    });
  }

  /**
   * 设置AI配置（兼容旧版本格式）
   * @param {object} config - AI配置对象
   * @returns {Promise<boolean>} 设置是否成功
   */
  async setAIConfig(config) {
    await this.initialize();
    
    // 转换为新版本格式
    const newConfig = {
      provider: this.detectProvider(config.apiUrl),
      model: config.model,
      apiKey: config.apiKey,
      baseUrl: this.extractBaseUrl(config.apiUrl),
      temperature: config.temperature,
      maxTokens: config.max_tokens,
      timeout: config.timeout
    };

    return await configManager.setConfig('ai', newConfig);
  }

  /**
   * 设置Flomo配置（兼容旧版本格式）
   * @param {object} config - Flomo配置对象
   * @returns {Promise<boolean>} 设置是否成功
   */
  async setFlomoConfig(config) {
    await this.initialize();
    
    const newConfig = {
      apiUrl: config.apiUrl,
      timeout: config.timeout
    };

    return await configManager.setConfig('flomo', newConfig);
  }

  /**
   * 设置侧边栏配置（兼容旧版本格式）
   * @param {object} config - 侧边栏配置对象
   * @returns {Promise<boolean>} 设置是否成功
   */
  async setSidepanelConfig(config) {
    await this.initialize();
    
    return await configManager.setConfig('sidepanel', config);
  }

  /**
   * 从API URL检测服务提供商
   * @param {string} apiUrl - API URL
   * @returns {string} 服务提供商名称
   */
  detectProvider(apiUrl) {
    if (!apiUrl) return 'siliconflow';
    
    if (apiUrl.includes('siliconflow.cn')) return 'siliconflow';
    if (apiUrl.includes('openrouter.ai')) return 'openrouter';
    if (apiUrl.includes('deepseek.com')) return 'deepseek';
    if (apiUrl.includes('moonshot.cn')) return 'moonshot';
    
    return 'siliconflow'; // 默认
  }

  /**
   * 从API URL提取基础URL
   * @param {string} apiUrl - API URL
   * @returns {string} 基础URL
   */
  extractBaseUrl(apiUrl) {
    if (!apiUrl) return 'https://api.siliconflow.cn/v1';
    
    try {
      const url = new URL(apiUrl);
      return `${url.protocol}//${url.host}/v1`;
    } catch {
      return 'https://api.siliconflow.cn/v1';
    }
  }

  /**
   * 验证配置
   * @param {string} section - 配置部分
   * @param {object} config - 配置对象
   * @returns {Promise<object>} 验证结果
   */
  async validateConfig(section, config) {
    await this.initialize();
    
    return await configManager.validateApiConfig(section, config);
  }

  /**
   * 获取所有配置（兼容旧版本格式）
   * @returns {Promise<object>} 所有配置
   */
  async getAllConfigs() {
    await this.initialize();
    
    const [aiConfig, flomoConfig, sidepanelConfig] = await Promise.all([
      this.getAIConfig(),
      this.getFlomoConfig(),
      this.getSidepanelConfig()
    ]);

    return {
      ai: aiConfig,
      flomo: flomoConfig,
      sidepanel: sidepanelConfig
    };
  }

  /**
   * 重置所有配置
   * @returns {Promise<boolean>} 重置是否成功
   */
  async resetAllConfigs() {
    await this.initialize();
    
    try {
      await Promise.all([
        configManager.resetConfig('ai'),
        configManager.resetConfig('flomo'),
        configManager.resetConfig('sidepanel')
      ]);
      
      return true;
    } catch (error) {
      console.error('重置配置失败:', error);
      return false;
    }
  }

  /**
   * 添加配置变更监听器
   * @param {string} section - 配置部分
   * @param {function} callback - 回调函数
   * @returns {function} 取消监听的函数
   */
  addConfigListener(section, callback) {
    return configManager.addListener(section, callback);
  }
}

// 创建全局适配器实例
export const legacyAdapter = new LegacyConfigAdapter();

// 为旧版本代码提供全局函数接口
if (typeof window !== 'undefined') {
  // 浏览器环境下的全局接口
  window.getAIConfig = () => legacyAdapter.getAIConfig();
  window.getFlomoConfig = () => legacyAdapter.getFlomoConfig();
  window.getSidepanelConfig = () => legacyAdapter.getSidepanelConfig();
  window.setAIConfig = (config) => legacyAdapter.setAIConfig(config);
  window.setFlomoConfig = (config) => legacyAdapter.setFlomoConfig(config);
  window.setSidepanelConfig = (config) => legacyAdapter.setSidepanelConfig(config);
}
