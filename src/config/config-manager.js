// 统一配置管理器
// 处理配置的获取、设置、验证和优先级管理

import { CONFIG_SCHEMA, CONFIG_PRIORITY, STORAGE_KEYS, ConfigValidator } from './config-schema.js';

export class ConfigManager {
  constructor() {
    this.cache = new Map();
    this.listeners = new Map();
    this.initialized = false;
  }

  /**
   * 初始化配置管理器
   */
  async initialize() {
    if (this.initialized) return;

    try {
      // 预加载所有配置
      await this.preloadConfigs();
      this.initialized = true;
      console.log('✅ 配置管理器初始化完成');
    } catch (error) {
      console.error('❌ 配置管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 预加载所有配置到缓存
   */
  async preloadConfigs() {
    const sections = Object.keys(CONFIG_SCHEMA);
    const promises = sections.map(section => this.getConfig(section));
    await Promise.all(promises);
  }

  /**
   * 获取指定部分的配置
   * @param {string} section - 配置部分名称
   * @returns {Promise<object>} 配置对象
   */
  async getConfig(section) {
    // 检查缓存
    if (this.cache.has(section)) {
      return this.cache.get(section);
    }

    try {
      const config = await this.buildConfig(section);
      this.cache.set(section, config);
      return config;
    } catch (error) {
      console.error(`获取 ${section} 配置失败:`, error);
      // 返回默认配置
      const defaults = ConfigValidator.getDefaults(section);
      this.cache.set(section, defaults);
      return defaults;
    }
  }

  /**
   * 构建配置（按优先级合并）
   * @param {string} section - 配置部分名称
   * @returns {Promise<object>} 合并后的配置
   */
  async buildConfig(section) {
    const configs = [];

    // 1. 默认配置（最低优先级）
    const defaults = ConfigValidator.getDefaults(section);
    configs.push({ priority: CONFIG_PRIORITY.DEFAULTS, config: defaults });

    // 2. 用户配置
    const userConfig = await this.getUserConfig(section);
    if (userConfig && Object.keys(userConfig).length > 0) {
      configs.push({ priority: CONFIG_PRIORITY.USER, config: userConfig });
    }

    // 3. 环境配置（最高优先级）
    const envConfig = this.getEnvironmentConfig(section);
    if (envConfig && Object.keys(envConfig).length > 0) {
      configs.push({ priority: CONFIG_PRIORITY.ENVIRONMENT, config: envConfig });
    }

    // 按优先级排序并合并
    configs.sort((a, b) => a.priority - b.priority);
    const mergedConfig = configs.reduce((result, item) => {
      return { ...result, ...item.config };
    }, {});

    return mergedConfig;
  }

  /**
   * 获取用户配置
   * @param {string} section - 配置部分名称
   * @returns {Promise<object>} 用户配置
   */
  async getUserConfig(section) {
    const storageKey = STORAGE_KEYS[section];
    if (!storageKey) return {};

    try {
      const result = await chrome.storage.sync.get(storageKey);
      return result[storageKey] || {};
    } catch (error) {
      console.warn(`获取用户配置 ${section} 失败:`, error);
      return {};
    }
  }

  /**
   * 获取环境配置
   * @param {string} section - 配置部分名称
   * @returns {object} 环境配置
   */
  getEnvironmentConfig(section) {
    // 环境配置功能已移除，不再支持硬编码的环境配置
    // 所有配置都应该通过用户设置或默认值来管理
    return {};
  }

  /**
   * 设置用户配置
   * @param {string} section - 配置部分名称
   * @param {object} config - 配置对象
   * @param {boolean} validate - 是否验证配置
   * @returns {Promise<boolean>} 设置是否成功
   */
  async setConfig(section, config, validate = true) {
    try {
      // 验证配置
      if (validate) {
        const validation = ConfigValidator.validateSection(section, config);
        if (!validation.valid) {
          throw new Error(`配置验证失败: ${validation.errors.join(', ')}`);
        }
      }

      // 保存到存储
      const storageKey = STORAGE_KEYS[section];
      if (storageKey) {
        await chrome.storage.sync.set({ [storageKey]: config });
      }

      // 更新缓存
      const currentConfig = await this.getConfig(section);
      const updatedConfig = { ...currentConfig, ...config };
      this.cache.set(section, updatedConfig);

      // 通知监听器
      this.notifyListeners(section, updatedConfig);

      console.log(`✅ ${section} 配置已更新`);
      return true;
    } catch (error) {
      console.error(`设置 ${section} 配置失败:`, error);
      throw error;
    }
  }

  /**
   * 获取特定配置项
   * @param {string} section - 配置部分名称
   * @param {string} key - 配置键名
   * @param {any} defaultValue - 默认值
   * @returns {Promise<any>} 配置值
   */
  async getConfigValue(section, key, defaultValue = null) {
    const config = await this.getConfig(section);
    return config[key] !== undefined ? config[key] : defaultValue;
  }

  /**
   * 设置特定配置项
   * @param {string} section - 配置部分名称
   * @param {string} key - 配置键名
   * @param {any} value - 配置值
   * @returns {Promise<boolean>} 设置是否成功
   */
  async setConfigValue(section, key, value) {
    const currentConfig = await this.getUserConfig(section);
    const updatedConfig = { ...currentConfig, [key]: value };
    return await this.setConfig(section, updatedConfig);
  }

  /**
   * 重置配置到默认值
   * @param {string} section - 配置部分名称
   * @returns {Promise<boolean>} 重置是否成功
   */
  async resetConfig(section) {
    try {
      const storageKey = STORAGE_KEYS[section];
      if (storageKey) {
        await chrome.storage.sync.remove(storageKey);
      }

      // 清除缓存，强制重新构建
      this.cache.delete(section);

      const defaultConfig = await this.getConfig(section);
      this.notifyListeners(section, defaultConfig);

      console.log(`✅ ${section} 配置已重置`);
      return true;
    } catch (error) {
      console.error(`重置 ${section} 配置失败:`, error);
      throw error;
    }
  }

  /**
   * 添加配置变更监听器
   * @param {string} section - 配置部分名称
   * @param {function} callback - 回调函数
   * @returns {function} 取消监听的函数
   */
  addListener(section, callback) {
    if (!this.listeners.has(section)) {
      this.listeners.set(section, new Set());
    }

    this.listeners.get(section).add(callback);

    // 返回取消监听的函数
    return () => {
      const sectionListeners = this.listeners.get(section);
      if (sectionListeners) {
        sectionListeners.delete(callback);
      }
    };
  }

  /**
   * 通知配置变更监听器
   * @param {string} section - 配置部分名称
   * @param {object} config - 新配置
   */
  notifyListeners(section, config) {
    const sectionListeners = this.listeners.get(section);
    if (sectionListeners) {
      sectionListeners.forEach(callback => {
        try {
          callback(config, section);
        } catch (error) {
          console.error(`配置监听器回调失败:`, error);
        }
      });
    }
  }

  /**
   * 清除所有缓存
   */
  clearCache() {
    this.cache.clear();
    console.log('✅ 配置缓存已清除');
  }

  /**
   * 验证API配置
   * @param {string} section - 配置部分名称
   * @param {object} config - 配置对象
   * @returns {Promise<object>} 验证结果
   */
  async validateApiConfig(section, config) {
    if (section !== 'ai' && section !== 'flomo') {
      return { valid: true };
    }

    try {
      if (section === 'ai') {
        return await this.validateAIConfig(config);
      } else if (section === 'flomo') {
        return await this.validateFlomoConfig(config);
      }
    } catch (error) {
      return {
        valid: false,
        error: error.message
      };
    }
  }

  /**
   * 验证AI配置
   * @param {object} config - AI配置
   * @returns {Promise<object>} 验证结果
   */
  async validateAIConfig(config) {
    if (!config.apiKey || !config.baseUrl) {
      return {
        valid: false,
        error: 'API密钥和基础URL不能为空'
      };
    }

    try {
      // 简单的连接测试
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(`${config.baseUrl}/models`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      return {
        valid: response.ok,
        error: response.ok ? null : `API连接失败: ${response.status}`
      };
    } catch (error) {
      return {
        valid: false,
        error: `API连接测试失败: ${error.message}`
      };
    }
  }

  /**
   * 验证Flomo配置
   * @param {object} config - Flomo配置
   * @returns {Promise<object>} 验证结果
   */
  async validateFlomoConfig(config) {
    if (!config.apiUrl) {
      return {
        valid: false,
        error: 'Flomo API地址不能为空'
      };
    }

    try {
      const url = new URL(config.apiUrl);
      if (url.protocol !== 'https:') {
        return {
          valid: false,
          error: 'Flomo API地址必须使用HTTPS协议'
        };
      }

      return { valid: true };
    } catch (error) {
      return {
        valid: false,
        error: 'Flomo API地址格式无效'
      };
    }
  }

  /**
   * 获取所有配置的摘要信息
   * @returns {Promise<object>} 配置摘要
   */
  async getConfigSummary() {
    const summary = {};
    const sections = Object.keys(CONFIG_SCHEMA);

    for (const section of sections) {
      try {
        const config = await this.getConfig(section);
        summary[section] = {
          configured: Object.keys(config).length > 0,
          keys: Object.keys(config)
        };
      } catch (error) {
        summary[section] = {
          configured: false,
          error: error.message
        };
      }
    }

    return summary;
  }
}

// 创建全局配置管理器实例
export const configManager = new ConfigManager();
