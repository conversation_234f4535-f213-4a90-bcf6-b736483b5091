// 统一Prompt模板管理
// 集中管理所有AI功能的prompt模板，支持模板变量替换

export const PROMPT_TEMPLATES = {
  // 标签生成模板
  tagGeneration: {
    // 新版本的结构化标签生成模板
    structured: `你是一位知识管理大师，精通使用 flomo 进行卡片笔记。你的任务是为下面的文本生成精准、结构化的 flomo 标签。

要求：
1. 识别文本的核心主题、概念、人名、项目名等。
2. 优先生成层级标签（例如：#技术/AI 或 #人物/马斯克），以体现概念的归属关系。
3. 如果没有明显的层级关系，则生成普通标签（例如：#灵感）。
4. 标签应高度概括、简短精炼。
5. 最终返回 3 到 5 个最合适的标签。
6. 以 #标签1 #标签2 的格式返回，标签之间用单个空格分隔。不要包含任何其他文字。

文本内容如下：
"""
{{content}}
"""`,

    // 简单标签生成模板（向后兼容）
    simple: `请为以下内容提取3-6个合适的标签，用中文回答，每个标签用空格分隔，不要包含#符号：

{{content}}`,

    // 带格式的标签生成模板
    formatted: `请为以下内容提取3-6个合适的标签，用中文回答，每个标签空格分隔，每个标签必须包含#符号：

{{content}}`
  },

  // 翻译模板
  translation: {
    // 智能翻译模板（自动检测语言）
    smart: `你是一位专业的双语翻译专家，精通中文和英文。

你的任务是自动识别以下文本的源语言（中文或英文），并将其翻译成另一种语言（英文或中文）,形成双语的展示效果，格式为：
{{content}}
-- 
{{translation}}

翻译要求：
1.  **准确流畅**：译文必须准确、流畅、自然。
2.  **保持原意**：忠实于原文的意义、语气和专业性。
3.  **格式纯净**：直接返回翻译后的文本，绝对不要添加任何前缀（如“译文：”）、标题、引号或任何形式的解释说明。

待翻译的文本：
"""
{{content}}
"""`,

    // 指定目标语言的翻译模板
    targeted: `请将以下内容翻译成{{targetLang}}，保持原意，只返回翻译结果：

{{content}}`,

    // 对照翻译模板
    bilingual: `请将以下内容进行双语对照翻译：
- 如果是中文，提供中英对照
- 如果是英文，提供英中对照
- 格式：原文 | 译文

内容：
{{content}}`
  },
};

// 模板变量替换工具
export class PromptTemplateEngine {
  /**
   * 替换模板中的变量
   * @param {string} template - 模板字符串
   * @param {object} variables - 变量对象
   * @returns {string} 替换后的字符串
   */
  static render(template, variables = {}) {
    let result = template;
    
    // 替换 {{variable}} 格式的变量
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      result = result.replace(regex, value || '');
    }
    
    return result;
  }

  /**
   * 获取指定功能和类型的模板
   * @param {string} feature - 功能名称 (tagGeneration, translation, etc.)
   * @param {string} type - 模板类型 (structured, simple, etc.)
   * @returns {string|null} 模板字符串
   */
  static getTemplate(feature, type = 'default') {
    const featureTemplates = PROMPT_TEMPLATES[feature];
    if (!featureTemplates) {
      console.warn(`未找到功能 ${feature} 的模板`);
      return null;
    }

    const template = featureTemplates[type] || featureTemplates.default || featureTemplates.simple;
    if (!template) {
      console.warn(`未找到功能 ${feature} 的 ${type} 类型模板`);
      return null;
    }

    return template;
  }

  /**
   * 渲染指定功能的模板
   * @param {string} feature - 功能名称
   * @param {string} type - 模板类型
   * @param {object} variables - 变量对象
   * @returns {string|null} 渲染后的prompt
   */
  static renderTemplate(feature, type, variables = {}) {
    const template = this.getTemplate(feature, type);
    if (!template) {
      return null;
    }

    return this.render(template, variables);
  }

  /**
   * 获取所有可用的模板功能
   * @returns {string[]} 功能名称数组
   */
  static getAvailableFeatures() {
    return Object.keys(PROMPT_TEMPLATES);
  }

  /**
   * 获取指定功能的所有模板类型
   * @param {string} feature - 功能名称
   * @returns {string[]} 模板类型数组
   */
  static getAvailableTypes(feature) {
    const featureTemplates = PROMPT_TEMPLATES[feature];
    return featureTemplates ? Object.keys(featureTemplates) : [];
  }
}

// 预定义的常用模板组合
export const TEMPLATE_PRESETS = {
  // 标签生成预设
  tags: {
    modern: { feature: 'tagGeneration', type: 'structured' },
    classic: { feature: 'tagGeneration', type: 'simple' },
    formatted: { feature: 'tagGeneration', type: 'formatted' }
  },

  // 翻译预设
  translate: {
    smart: { feature: 'translation', type: 'smart' },
    bilingual: { feature: 'translation', type: 'bilingual' },
    targeted: { feature: 'translation', type: 'targeted' }
  },

  // 摘要预设
  summary: {
    brief: { feature: 'summary', type: 'brief' },
    detailed: { feature: 'summary', type: 'detailed' },
    points: { feature: 'summary', type: 'bulletPoints' }
  },

  // 格式化预设
  format: {
    structure: { feature: 'formatting', type: 'structure' },
    language: { feature: 'formatting', type: 'language' },
    full: { feature: 'formatting', type: 'comprehensive' }
  }
};
