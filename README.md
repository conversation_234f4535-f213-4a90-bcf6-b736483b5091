# Save to Flomo - Chrome扩展

一个简洁高效的Chrome扩展，用于将网页内容快速保存到Flomo笔记应用，支持AI智能处理。

## ✨ 功能特性

### 🚀 核心功能
- **快速保存**: 选中网页文字后，通过右键菜单或侧边栏快速保存到Flomo
- **智能处理**: 支持HTML格式转换，保持内容结构
- **多种保存方式**: 右键菜单直接保存 | 侧边栏编辑保存

### 🤖 AI增强功能
- **智能标签生成**: 使用AI自动为内容生成3-6个相关标签
- **内容翻译**: 支持中英文智能互译
- **多AI模型支持**: SiliconFlow、OpenRouter、DeepSeek、Moonshot AI

### 🎨 用户体验
- **侧边栏自动关闭**: 保存成功后1秒自动关闭（可配置）
- **AI功能互斥**: 防止多个AI操作同时进行，避免数据冲突
- **统一配置管理**: 集中管理所有配置项，支持环境变量和用户设置
- **完善错误处理**: 智能降级机制，确保功能稳定性

## 📦 安装使用

### 1. 安装扩展
```bash
1. 下载扩展文件
2. 打开 chrome://extensions/
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择扩展文件夹
```

### 2. 配置API
```bash
1. 点击扩展图标打开配置页面
2. 设置Flomo API地址（必需）
3. 配置AI服务（可选）
```

### 3. 使用方法
- **快速保存**: 选中文字 → 右键菜单 → "保存到Flomo"
- **编辑保存**: 选中文字 → 点击扩展图标 → 侧边栏编辑 → 保存

## ⚙️ 配置说明

### Flomo配置
- **API地址**: 你的Flomo API地址（必需）

### AI配置（可选）
- **API提供商**: SiliconFlow、OpenRouter、DeepSeek、Moonshot AI
- **API密钥**: 对应的API密钥
- **模型选择**: 选择使用的AI模型

### 侧边栏配置
- **自动关闭**: 保存后是否自动关闭侧边栏（默认开启）
- **关闭延迟**: 自动关闭的延迟时间（默认1000ms）

## 🏗️ 项目结构

```
chrome-plugin-save-to-flomo/
├── manifest.json              # 扩展配置文件
├── background.js              # 后台服务脚本
├── content.js                 # 内容脚本
├── popup.html/js              # 配置页面
├── sidepanel.html/js/css      # 侧边栏页面
├── ai-config.js               # AI配置管理
├── src/config/                # 统一配置管理系统
│   ├── config-manager.js      # 核心配置管理器
│   ├── config-schema.js       # 配置模式定义
│   ├── prompts.js             # AI提示词模板管理
│   └── adapters/
│       └── legacy-adapter.js  # 向后兼容适配器
└── test.html                  # 功能测试页面
```

## 🧪 测试

打开 `test.html` 进行功能测试：
- ✅ 配置管理测试
- ✅ AI功能测试  
- ✅ 存储和数据测试
- ✅ UI交互测试

## 🔧 开发指南

### 开发环境设置
```bash
1. 克隆项目到本地
2. 在Chrome中加载扩展（开发者模式）
3. 修改代码后重新加载扩展
4. 使用test.html验证功能
```

### 核心技术特性
- **统一配置管理**: 优先级管理（环境变量 > 用户设置 > 默认值）
- **模板化提示词**: 可配置的AI提示词模板，支持变量替换
- **向后兼容**: 支持旧版本配置格式的自动迁移
- **智能降级**: 配置加载失败时的降级机制

## 📝 更新日志

### v2.0 (当前版本)
- 🔧 重构统一配置管理系统
- 🤖 统一AI提示词模板管理
- 🎨 优化用户体验和界面
- 📦 精简项目结构，删除冗余代码
- ✅ 完善测试体系

### v1.x
- 🚀 基础保存功能
- 🤖 AI增强功能
- 🎨 侧边栏支持

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！
