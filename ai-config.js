// AI配置文件 - 使用统一配置管理系统
// 此文件现在作为配置适配器，从统一配置管理器获取配置

// 动态AI配置（从统一配置管理器获取）
let AI_CONFIG = null;

// 初始化AI配置
async function initializeAIConfig() {
  try {
    // 导入统一配置管理器
    const { configManager } = await import('./src/config/config-manager.js');
    await configManager.initialize();

    // 获取AI配置
    const aiConfig = await configManager.getConfig('ai');

    // 转换为旧版本格式以保持兼容性
    AI_CONFIG = {
      // API配置
      apiUrl: `${aiConfig.baseUrl}/chat/completions`,
      apiKey: aiConfig.apiKey,
      model: aiConfig.model,

      // API请求参数
      temperature: aiConfig.temperature,
      max_tokens: aiConfig.maxTokens,

      // 功能配置（保持向后兼容）
      maxTags: 6,
      timeout: aiConfig.timeout
    };

    console.log('✅ AI配置已从统一配置管理器加载');
    return AI_CONFIG;
  } catch (error) {
    console.error('❌ 无法加载AI配置:', error);
    // 不再使用硬编码的默认配置，直接抛出错误
    AI_CONFIG = null;
    throw new Error(`AI配置加载失败: ${error.message}`);
  }
}

// 获取标签生成Prompt（使用统一模板）
async function getTagGenerationPrompt(content, type = 'simple') {
  try {
    const { PromptTemplateEngine } = await import('./src/config/prompts.js');
    return PromptTemplateEngine.renderTemplate('tagGeneration', type, { content });
  } catch (error) {
    console.warn('⚠️ 无法加载Prompt模板，使用默认模板:', error);
    // 降级到默认模板
    return `请为以下内容提取3-6个合适的标签，用中文回答，每个标签用逗号分隔，不要包含#符号：\n\n${content}`;
  }
}

// 获取翻译Prompt（使用统一模板）
async function getTranslationPrompt(content, targetLang = null) {
  try {
    const { PromptTemplateEngine } = await import('./src/config/prompts.js');
    const type = targetLang ? 'targeted' : 'smart';
    return PromptTemplateEngine.renderTemplate('translation', type, { content, targetLang });
  } catch (error) {
    console.warn('⚠️ 无法加载Prompt模板，使用默认模板:', error);
    // 降级到默认模板
    const defaultTargetLang = targetLang || '中文';
    return `请将以下内容翻译成${defaultTargetLang}，保持原意，只返回翻译结果：\n\n${content}`;
  }
}

// 导出配置和函数（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    AI_CONFIG,
    initializeAIConfig,
    getTagGenerationPrompt,
    getTranslationPrompt
  };
}

// 全局变量（如果在浏览器环境中）
if (typeof window !== 'undefined') {
  window.AI_CONFIG = AI_CONFIG;
  window.initializeAIConfig = initializeAIConfig;
  window.getTagGenerationPrompt = getTagGenerationPrompt;
  window.getTranslationPrompt = getTranslationPrompt;
}
