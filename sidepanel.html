<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <title>Flomo 保存工具</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background: #f8f9fa;
      font-size: 14px;
      line-height: 1.5;
    }

    .container {
      max-width: 100%;
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    h1 {
      margin: 0 0 20px 0;
      font-size: 20px;
      color: #333;
      text-align: center;
    }

    .content-area {
      margin-bottom: 20px;
    }

    .content-info {
      background: #e8f0fe;
      border: 1px solid #1a73e8;
      border-radius: 4px;
      padding: 12px;
      margin-bottom: 16px;
      font-size: 13px;
      color: #1a73e8;
    }

    textarea {
      width: 100%;
      min-height: 200px;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      font-family: inherit;
      resize: vertical;
      box-sizing: border-box;
    }

    textarea:focus {
      outline: none;
      border-color: #1a73e8;
      box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
    }

    .ai-tools {
      display: flex;
      gap: 8px;
      margin: 16px 0;
      flex-wrap: wrap;
    }

    .ai-btn {
      padding: 8px 16px;
      background: #f1f3f4;
      border: 1px solid #dadce0;
      border-radius: 4px;
      cursor: pointer;
      font-size: 13px;
      color: #5f6368;
      transition: all 0.2s;
    }

    .ai-btn:hover {
      background: #e8eaed;
      border-color: #5f6368;
    }

    .ai-btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .save-btn {
      width: 100%;
      padding: 12px;
      background: #1a73e8;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
      margin-top: 16px;
    }

    .save-btn:hover {
      background: #1557b0;
    }

    .save-btn:disabled {
      background: #dadce0;
      cursor: not-allowed;
    }

    .status {
      margin-top: 12px;
      padding: 10px;
      border-radius: 4px;
      text-align: center;
      font-size: 13px;
      display: none;
    }

    .status.success {
      background: #e6f4ea;
      color: #137333;
      border: 1px solid #34a853;
    }

    .status.error {
      background: #fce8e6;
      color: #d93025;
      border: 1px solid #ea4335;
    }

    .loading {
      opacity: 0.7;
      pointer-events: none;
    }

    .loading::after {
      content: '';
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid #ccc;
      border-top: 2px solid #1a73e8;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-left: 8px;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }
  </style>
</head>

<body>
  <div class="container">
    <h1>📝 Flomo 保存工具</h1>

    <div class="content-area">
      <div id="content-info" class="content-info" style="display: none;">
        来源：<span id="page-title"></span><br>
        链接：<span id="page-url"></span>
      </div>

      <textarea id="content-text" placeholder="选择页面文字后，内容会自动显示在这里..."></textarea>
    </div>

    <div class="ai-tools">
      <button class="ai-btn" id="generate-tags">🏷️ 生成标签</button>
      <button class="ai-btn" id="translate-text">🌐 翻译</button>
    </div>

    <button class="save-btn" id="save-to-flomo">保存到 Flomo</button>

    <div id="status-message" class="status"></div>
  </div>

  <script src="sidepanel.js"></script>
</body>

</html>