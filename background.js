
// 简洁的后台脚本 - 只处理核心功能

// 安装时创建右键菜单
chrome.runtime.onInstalled.addListener(() => {
  chrome.contextMenus.create({
    id: "save-to-flomo",
    title: "保存到 Flomo",
    contexts: ["selection"]
  });
});

// 右键菜单点击处理
chrome.contextMenus.onClicked.addListener(async (info, tab) => {
  if (info.menuItemId === "save-to-flomo") {
    try {
      // 打开侧边栏
      await chrome.sidePanel.open({ tabId: tab.id });

      // 获取页面信息
      const [pageResult] = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        function: () => ({
          title: document.title,
          url: window.location.href
        })
      });

      // 存储选中的内容和页面信息
      await chrome.storage.local.set({
        'pendingContent': {
          selectedText: info.selectionText,
          pageTitle: pageResult.result.title,
          pageUrl: pageResult.result.url,
          timestamp: Date.now()
        }
      });
    } catch (error) {
      console.error('处理选中内容失败:', error);
    }
  }
});

// 处理来自侧边栏的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'saveToFlomo') {
    saveToFlomo(message.content)
      .then(result => sendResponse({ success: true, result }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true; // 保持消息通道开放
  }
});

// 保存内容到Flomo
async function saveToFlomo(content) {
  const { flomoApiUrl } = await chrome.storage.sync.get('flomoApiUrl');

  if (!flomoApiUrl) {
    throw new Error('请先配置 Flomo API 地址');
  }

  const response = await fetch(flomoApiUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      content: content.trim()
    })
  });

  if (!response.ok) {
    throw new Error(`保存失败: ${response.status} ${response.statusText}`);
  }

  return await response.json();
}
