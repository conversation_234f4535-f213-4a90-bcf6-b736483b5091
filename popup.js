// 简化的 popup.js
document.addEventListener('DOMContentLoaded', () => {
  const apiUrlInput = document.getElementById('api-url');
  const autoCloseCheckbox = document.getElementById('auto-close');
  const saveButton = document.getElementById('save-button');
  const statusMessage = document.getElementById('status-message');

  // 加载已保存的设置
  chrome.storage.sync.get(['flomoApiUrl', 'sidepanelConfig'], (data) => {
    if (data.flomoApiUrl) {
      apiUrlInput.value = data.flomoApiUrl;
    }

    // 加载侧边栏配置
    const sidepanelConfig = {
      autoClose: true, // 默认自动关闭
      ...(data.sidepanelConfig || {})
    };
    autoCloseCheckbox.checked = sidepanelConfig.autoClose;
  });

  // URL 验证
  const isValidUrl = (string) => {
    try {
      const url = new URL(string);
      return url.protocol === "https:";
    } catch (_) {
      return false;
    }
  };

  // 显示状态消息
  const showStatus = (message, type) => {
    statusMessage.textContent = message;
    statusMessage.className = `status ${type}`;
    statusMessage.style.display = 'block';

    if (type === 'success') {
      setTimeout(() => {
        statusMessage.style.display = 'none';
      }, 2000);
    }
  };

  // 保存设置
  saveButton.addEventListener('click', () => {
    const apiUrl = apiUrlInput.value.trim();
    const autoClose = autoCloseCheckbox.checked;

    if (!apiUrl) {
      showStatus('API 地址不能为空', 'error');
      return;
    }

    if (!isValidUrl(apiUrl)) {
      showStatus('请输入有效的 HTTPS 地址', 'error');
      return;
    }

    // 准备侧边栏配置
    const sidepanelConfig = {
      autoClose: autoClose,
      autoCloseDelay: 1000 // 1秒后关闭
    };

    // 保存设置
    chrome.storage.sync.set({
      flomoApiUrl: apiUrl,
      sidepanelConfig: sidepanelConfig
    }, () => {
      showStatus('设置已保存', 'success');
      setTimeout(() => window.close(), 1000);
    });
  });
});
