
// 简化的内容脚本 - 专注于页面内容提取

// 获取当前选中的文本内容
function getSelectedContent() {
  const selection = window.getSelection();
  if (selection.rangeCount === 0) {
    return null;
  }

  const selectedText = selection.toString().trim();
  if (!selectedText) {
    return null;
  }

  return {
    text: selectedText,
    length: selectedText.length
  };
}

// 监听来自扩展的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'getSelectedContent') {
    const selectedContent = getSelectedContent();
    sendResponse({
      success: true,
      data: selectedContent
    });
  }
  return true;
});
